<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html b:version='2' class='v2' expr:dir='data:blog.languageDirection' xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
<head>
  <!-- Meta Tags -->
  <meta charset='UTF-8'/>
  <meta name='viewport' content='width=device-width, initial-scale=1.0'/>
  <meta name='robots' content='index, follow'/>
  <meta name='author' content='WebToolsKit'/>
  <meta name='generator' content='Blogger'/>

  <!-- Dynamic Title -->
  <b:if cond='data:blog.pageType == &quot;index&quot;'>
    <title><data:blog.title/> - Professional Web Tools and Resources</title>
    <meta name='description' content='WebToolsKit offers professional web development tools, SEO utilities, and comprehensive resources for developers and digital marketers.'/>
  <b:else/>
    <title><data:blog.pageName/> | <data:blog.title/></title>
    <meta expr:content='data:blog.metaDescription' name='description'/>
  </b:if>

  <!-- Canonical URL -->
  <link expr:href='data:blog.canonicalUrl' rel='canonical'/>

  <!-- RSS and Atom Feeds -->
  <link rel='alternate' type='application/atom+xml' title='webtoolskit - Atom' href='https://webtoolskit1.blogspot.com/feeds/posts/default'/>
  <link rel='alternate' type='application/rss+xml' title='webtoolskit - RSS' href='https://www.webtoolskit.org/feeds/posts/default?alt=rss'/>
  <link rel='service.post' type='application/atom+xml' title='webtoolskit - Atom' href='https://www.blogger.com/feeds/5088979447855026968/posts/default'/>

  <!-- Open Graph Tags -->
  <meta expr:content='data:blog.pageTitle' property='og:title'/>
  <meta expr:content='data:blog.metaDescription' property='og:description'/>
  <meta content='website' property='og:type'/>
  <meta expr:content='data:blog.canonicalUrl' property='og:url'/>
  <meta content='https://webtoolskit.org/favicon.png' property='og:image'/>
  <meta content='WebToolsKit' property='og:site_name'/>

  <!-- Twitter Cards -->
  <meta content='summary_large_image' name='twitter:card'/>
  <meta content='@webtoolskit' name='twitter:site'/>
  <meta expr:content='data:blog.pageTitle' name='twitter:title'/>
  <meta expr:content='data:blog.metaDescription' name='twitter:description'/>
  <meta content='https://webtoolskit.org/favicon.png' name='twitter:image'/>

  <!-- Preconnect for Performance -->
  <link href='https://fonts.googleapis.com' rel='preconnect'/>
  <link crossorigin='' href='https://fonts.gstatic.com' rel='preconnect'/>

  <!-- Favicon -->
  <link href='https://webtoolskit.org/favicon.ico' rel='icon' type='image/x-icon'/>

  <!-- Structured Data -->
  <script type='application/ld+json'>
  {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "WebToolsKit",
    "url": "https://webtoolskit.org",
    "description": "Professional web development tools, SEO utilities, and comprehensive resources for developers and digital marketers.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://webtoolskit.org/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": "WebToolsKit",
      "url": "https://webtoolskit.org",
      "logo": {
        "@type": "ImageObject",
        "url": "https://webtoolskit.org/logo.png"
      }
    }
  }
  </script>

  <b:skin><![CDATA[
  /* CSS Reset and Base Styles */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  :root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #3b82f6;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --border-color: #e5e7eb;
    --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
  }

  /* Header Styles */
  .header-wrapper {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow);
  }

  .header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .blog-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .blog-title:hover {
    color: var(--secondary-color);
  }

  /* Navigation */
  .main-nav {
    display: flex;
    gap: 2rem;
    list-style: none;
  }

  .main-nav a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
  }

  .main-nav a:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
  }

  /* Main Content */
  .main-wrapper {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 2rem;
  }

  /* Tools Section */
  .tools-section {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
  }

  .tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
  }

  .tool-card {
    background: var(--bg-primary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
  }

  .tool-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .tool-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
  }

  .tool-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .tool-link {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
  }

  .tool-link:hover {
    background: var(--secondary-color);
  }

  /* Blog Posts */
  .blog-posts {
    display: grid;
    gap: 2rem;
  }

  .post-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    transition: var(--transition);
  }

  .post-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  .post-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  .post-content {
    padding: 1.5rem;
  }

  .post-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  .post-title a {
    color: var(--text-primary);
    text-decoration: none;
  }

  .post-title a:hover {
    color: var(--primary-color);
  }

  .post-meta {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .post-excerpt {
    color: var(--text-secondary);
    line-height: 1.6;
  }

  /* Sidebar */
  .sidebar {
    display: grid;
    gap: 2rem;
  }

  .widget {
    background: var(--bg-primary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
  }

  .widget-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
  }

  /* Footer */
  .footer-wrapper {
    background: var(--text-primary);
    color: white;
    margin-top: 4rem;
  }

  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
    text-align: center;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .main-wrapper {
      grid-template-columns: 1fr;
    }

    .header-container {
      flex-direction: column;
      gap: 1rem;
    }

    .main-nav {
      flex-wrap: wrap;
      justify-content: center;
    }

    .tools-grid {
      grid-template-columns: 1fr;
    }
  }

  /* Dark Mode Support */
  @media (prefers-color-scheme: dark) {
    :root {
      --text-primary: #f9fafb;
      --text-secondary: #d1d5db;
      --bg-primary: #1f2937;
      --bg-secondary: #374151;
      --border-color: #4b5563;
    }
  }

  /* Search Form */
  .search-form {
    display: flex;
    gap: 0.5rem;
    margin: 1rem 0;
  }

  .search-input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
  }

  .search-button {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
  }

  .search-button:hover {
    background: var(--secondary-color);
  }

  /* Pagination */
  .blog-pager {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;
  }

  .blog-pager a {
    background: var(--primary-color);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: var(--transition);
  }

  .blog-pager a:hover {
    background: var(--secondary-color);
  }

  /* Labels/Tags */
  .post-labels {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .post-labels a {
    background: var(--bg-secondary);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.85rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
  }

  .post-labels a:hover {
    background: var(--primary-color);
    color: white;
  }

  /* Related Posts */
  .related-posts {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-top: 2rem;
  }

  .related-posts h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
  }

  .related-posts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
  }

  .related-post {
    background: var(--bg-primary);
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
  }

  .related-post a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
  }

  .related-post a:hover {
    color: var(--primary-color);
  }

  /* Social Share Buttons */
  .social-share {
    display: flex;
    gap: 1rem;
    margin: 1.5rem 0;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
  }

  .share-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: white;
    font-weight: 500;
    transition: var(--transition);
  }

  .share-facebook { background: #1877f2; }
  .share-twitter { background: #1da1f2; }
  .share-linkedin { background: #0077b5; }
  .share-whatsapp { background: #25d366; }

  .share-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  /* Breadcrumbs */
  .breadcrumbs {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    font-size: 0.9rem;
  }

  .breadcrumbs a {
    color: var(--primary-color);
    text-decoration: none;
  }

  .breadcrumbs a:hover {
    text-decoration: underline;
  }

  /* Loading Animation */
  .loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to { transform: rotate(360deg); }
  }

  /* Enhanced Post Styles */
  .post-full {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
    overflow: hidden;
  }

  .post-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--border-color);
  }

  .post-category {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-decoration: none;
    margin-bottom: 1rem;
  }

  .post-category:hover {
    background: var(--secondary-color);
  }

  .post-title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--text-primary);
  }

  .post-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .author-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .author-avatar {
    border-radius: 50%;
    object-fit: cover;
  }

  .author-name {
    font-weight: 500;
    color: var(--text-primary);
  }

  .post-date {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }

  .reading-time {
    color: var(--text-secondary);
  }

  /* Table of Contents */
  .table-of-contents {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    border-left: 4px solid var(--primary-color);
  }

  .table-of-contents h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
  }

  .table-of-contents ul {
    list-style: none;
    padding: 0;
  }

  .table-of-contents li {
    margin-bottom: 0.5rem;
  }

  .table-of-contents a {
    color: var(--text-primary);
    text-decoration: none;
    padding: 0.25rem 0;
    display: block;
    transition: var(--transition);
  }

  .table-of-contents a:hover {
    color: var(--primary-color);
    padding-left: 0.5rem;
  }

  /* Contact Form */
  .contact-form-container {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: var(--border-radius);
    margin: 2rem 0;
  }

  .contact-form .form-group {
    margin-bottom: 1.5rem;
  }

  .contact-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
  }

  .contact-form input,
  .contact-form textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
  }

  .contact-form input:focus,
  .contact-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  }

  .submit-button {
    background: var(--primary-color);
    color: white;
    padding: 0.75rem 2rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
  }

  .submit-button:hover {
    background: var(--secondary-color);
  }

  /* Post Footer */
  .post-footer {
    padding: 2rem;
    border-top: 1px solid var(--border-color);
  }

  .post-labels {
    margin-bottom: 2rem;
  }

  .tag-link {
    display: inline-block;
    background: var(--bg-secondary);
    color: var(--primary-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    text-decoration: none;
    font-size: 0.85rem;
    margin: 0.25rem 0.5rem 0.25rem 0;
    border: 1px solid var(--border-color);
    transition: var(--transition);
  }

  .tag-link:hover {
    background: var(--primary-color);
    color: white;
  }

  /* Enhanced Social Share */
  .social-share {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
  }

  .social-share h4 {
    margin-bottom: 1rem;
    color: var(--text-primary);
  }

  .share-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .share-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    color: white;
    font-weight: 500;
    transition: var(--transition);
    min-width: 120px;
    justify-content: center;
  }

  .share-button.facebook { background: #1877f2; }
  .share-button.twitter { background: #1da1f2; }
  .share-button.linkedin { background: #0077b5; }
  .share-button.whatsapp { background: #25d366; }
  .share-button.email { background: #6b7280; }

  .share-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }

  /* Author Bio */
  .author-bio {
    display: flex;
    gap: 1.5rem;
    padding: 2rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    margin: 2rem 0;
  }

  .author-avatar-large img {
    border-radius: 50%;
    object-fit: cover;
  }

  .author-bio .author-info h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
  }

  .author-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
  }

  .author-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
  }

  .author-link:hover {
    text-decoration: underline;
  }

  /* Post Navigation */
  .post-navigation {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
  }

  .post-navigation a {
    display: flex;
    flex-direction: column;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition);
    flex: 1;
    max-width: 45%;
  }

  .post-navigation a:hover {
    background: var(--primary-color);
    color: white;
  }

  .nav-direction {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.25rem;
  }

  .nav-title {
    font-weight: 500;
  }

  .nav-next {
    text-align: right;
  }

  /* Print Styles */
  @media print {
    .header-wrapper,
    .sidebar,
    .footer-wrapper,
    .social-share,
    .search-form,
    .post-navigation,
    .author-bio {
      display: none;
    }

    .main-wrapper {
      grid-template-columns: 1fr;
      max-width: none;
      margin: 0;
      padding: 0;
    }

    .post-card,
    .tool-card,
    .post-full {
      box-shadow: none;
      border: 1px solid #ccc;
      break-inside: avoid;
    }

    .post-title {
      font-size: 1.8rem;
    }
  }
  ]]></b:skin>
</head>

<body>
  <!-- Header Section -->
  <header class='header-wrapper'>
    <div class='header-container'>
      <b:section class='header' id='header' maxwidgets='1' showaddelement='no'>
        <b:widget id='Header1' locked='true' title='WebToolsKit (Header)' type='Header' version='2'>
          <b:widget-settings>
            <b:widget-setting name='displayUrl'/>
            <b:widget-setting name='displayHeight'>0</b:widget-setting>
            <b:widget-setting name='sectionWidth'>0</b:widget-setting>
            <b:widget-setting name='useImage'>false</b:widget-setting>
            <b:widget-setting name='shrinkToFit'>false</b:widget-setting>
            <b:widget-setting name='imagePlacement'>REPLACE</b:widget-setting>
            <b:widget-setting name='displayWidth'>0</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <b:if cond='data:useImage'>
              <b:if cond='data:imagePlacement == &quot;REPLACE&quot;'>
                <a expr:href='data:blog.homepageUrl'>
                  <img expr:alt='data:title' expr:height='data:height' expr:id='data:widget.instanceId + &quot;_headerimg&quot;' expr:src='data:sourceUrl' expr:width='data:width' style='display: block'/>
                </a>
              <b:else/>
                <a class='blog-title' expr:href='data:blog.homepageUrl'>
                  <img expr:alt='data:title' expr:height='data:height' expr:id='data:widget.instanceId + &quot;_headerimg&quot;' expr:src='data:sourceUrl' expr:width='data:width' style='display: inline; margin-right: 5px;'/>
                  <data:title/>
                </a>
              </b:if>
            <b:else/>
              <a class='blog-title' expr:href='data:blog.homepageUrl'>
                <span>🛠️</span>
                <data:title/>
              </a>
            </b:if>
          </b:includable>

          <b:includable id='title'>
            <data:title/>
          </b:includable>
        </b:widget>
      </b:section>

      <nav aria-label='Main navigation'>
        <ul class='main-nav'>
          <li><a href='https://webtoolskit.org'>Home</a></li>
          <li><a href='https://webtoolskit.org/tools'>Tools</a></li>
          <li><a href='https://webtoolskit.org/blog'>Blog</a></li>
          <li><a href='https://webtoolskit.org/about'>About</a></li>
          <li><a href='https://webtoolskit.org/contact'>Contact</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <!-- Main Content -->
  <main class='main-wrapper'>
    <div class='content-area'>
      <!-- Featured Tools Section -->
      <section class='tools-section'>
        <h2>Featured Tools</h2>
        <div class='tools-grid'>
          <div class='tool-card'>
            <h3 class='tool-title'>SEO Analyzer</h3>
            <p class='tool-description'>Comprehensive SEO analysis tool to optimize your website's search engine performance.</p>
            <a href='#' class='tool-link'>Try Now</a>
          </div>
          <div class='tool-card'>
            <h3 class='tool-title'>Code Formatter</h3>
            <p class='tool-description'>Format and beautify your HTML, CSS, JavaScript, and other code with ease.</p>
            <a href='#' class='tool-link'>Try Now</a>
          </div>
          <div class='tool-card'>
            <h3 class='tool-title'>Image Optimizer</h3>
            <p class='tool-description'>Compress and optimize images for web without losing quality.</p>
            <a href='#' class='tool-link'>Try Now</a>
          </div>
          <div class='tool-card'>
            <h3 class='tool-title'>Password Generator</h3>
            <p class='tool-description'>Generate secure, random passwords with customizable options.</p>
            <a href='#' class='tool-link'>Try Now</a>
          </div>
        </div>
      </section>

      <!-- Blog Posts Section -->
      <section class='blog-posts'>
        <b:section class='main' id='main' showaddelement='no'>
          <b:widget id='Blog1' locked='true' title='Blog Posts' type='Blog' version='2'>
            <b:widget-settings>
              <b:widget-setting name='showDateHeader'>false</b:widget-setting>
              <b:widget-setting name='style.textcolor'>#000000</b:widget-setting>
              <b:widget-setting name='showShareButtons'>true</b:widget-setting>
              <b:widget-setting name='authorLabel'>By:</b:widget-setting>
              <b:widget-setting name='showComments'>true</b:widget-setting>
              <b:widget-setting name='showBacklinks'>false</b:widget-setting>
              <b:widget-setting name='style.urlcolor'>#0000FF</b:widget-setting>
              <b:widget-setting name='showInlineAds'>false</b:widget-setting>
              <b:widget-setting name='showReactions'>false</b:widget-setting>
            </b:widget-settings>
            <b:includable id='main' var='top'>
              <!-- Breadcrumbs -->
              <b:if cond='data:blog.pageType != &quot;index&quot;'>
                <nav class='breadcrumbs' aria-label='Breadcrumb'>
                  <a href='https://webtoolskit.org'>Home</a> &#62;
                  <b:if cond='data:blog.pageType == &quot;item&quot;'>
                    <a href='https://webtoolskit.org/blog'>Blog</a> &#62;
                    <span><data:blog.pageName/></span>
                  <b:else/>
                    <span><data:blog.pageName/></span>
                  </b:if>
                </nav>
              </b:if>

              <b:if cond='data:blog.pageType == &quot;index&quot;'>
                <!-- Home Page Posts -->
                <b:loop values='data:posts' var='post'>
                  <article class='post-card' itemscope='itemscope' itemtype='http://schema.org/BlogPosting'>
                    <b:if cond='data:post.featuredImage'>
                      <img class='post-image' expr:alt='data:post.title' expr:src='data:post.featuredImage' loading='lazy'/>
                    </b:if>
                    <div class='post-content'>
                      <h2 class='post-title' itemprop='headline'>
                        <a expr:href='data:post.url' itemprop='url'><data:post.title/></a>
                      </h2>
                      <div class='post-meta'>
                        <time expr:datetime='data:post.timestampISO8601' itemprop='datePublished'>
                          <data:post.timestamp/>
                        </time>
                        <span itemprop='author' itemscope='itemscope' itemtype='http://schema.org/Person'>
                          by <span itemprop='name'><data:post.author/></span>
                        </span>
                      </div>
                      <div class='post-excerpt' itemprop='description'>
                        <data:post.snippet/>
                      </div>
                      <b:if cond='data:post.labels'>
                        <div class='post-labels'>
                          <b:loop values='data:post.labels' var='label'>
                            <a expr:href='data:label.url' rel='tag'><data:label.name/></a>
                          </b:loop>
                        </div>
                      </b:if>
                    </div>
                  </article>
                </b:loop>

                <!-- Pagination -->
                <b:include name='nextprev'/>

              <b:else/>
                <!-- Single Post Page -->
                <b:loop values='data:posts' var='post'>
                  <article class='post-full hentry' itemscope='itemscope' itemtype='http://schema.org/BlogPosting'>
                    <b:class cond='data:view.isSingleItem' name='single-post'/>
                    <b:class cond='data:view.isPage' name='static-page'/>

                    <!-- Structured Data for Individual Posts -->
                    <b:if cond='data:view.isSingleItem'>
                      <script type='application/ld+json'>
                      {
                        "@context": "https://schema.org",
                        "@type": "BlogPosting",
                        "headline": "<data:post.title.escaped/>",
                        "description": "<data:post.snippet.escaped/>",
                        "image": "<b:eval expr='data:post.featuredImage ? data:post.featuredImage : &quot;https://webtoolskit.org/default-image.jpg&quot;'/>",
                        "author": {
                          "@type": "Person",
                          "name": "<data:post.author.name.escaped/>",
                          "url": "<b:eval expr='data:post.author.profileUrl ? data:post.author.profileUrl : &quot;#&quot;'/>"
                        },
                        "datePublished": "<data:post.date.iso8601/>",
                        "dateModified": "<data:post.lastUpdated.iso8601/>",
                        "publisher": {
                          "@type": "Organization",
                          "name": "WebToolsKit",
                          "logo": {
                            "@type": "ImageObject",
                            "url": "https://webtoolskit.org/logo.png",
                            "width": 250,
                            "height": 70
                          }
                        },
                        "mainEntityOfPage": {
                          "@type": "WebPage",
                          "@id": "<data:post.url.canonical/>"
                        },
                        "url": "<data:post.url.canonical/>"
                      }
                      </script>
                    </b:if>

                    <!-- Post Header -->
                    <header class='post-header'>
                      <b:if cond='data:post.labels.first'>
                        <span class='post-category'>
                          <a expr:href='data:post.labels.first.url' rel='tag'><data:post.labels.first.name/></a>
                        </span>
                      </b:if>

                      <b:if cond='data:view.isSingleItem'>
                        <h1 class='post-title entry-title' itemprop='headline'><data:post.title/></h1>
                      <b:else/>
                        <h2 class='post-title entry-title' itemprop='headline'>
                          <a expr:href='data:post.url' rel='bookmark'><data:post.title/></a>
                        </h2>
                      </b:if>

                      <div class='post-meta'>
                        <div class='author-info'>
                          <b:if cond='data:post.author.authorPhoto.image'>
                            <img class='author-avatar' expr:src='data:post.author.authorPhoto.image' expr:alt='data:post.author.name' width='32' height='32'/>
                          </b:if>
                          <span class='author-name' itemprop='author' itemscope='itemscope' itemtype='http://schema.org/Person'>
                            <span itemprop='name'><data:post.author.name/></span>
                          </span>
                        </div>
                        <div class='post-date'>
                          <time expr:datetime='data:post.date.iso8601' itemprop='datePublished'>
                            <data:post.date/>
                          </time>
                          <b:if cond='data:view.isSingleItem'>
                            <span class='reading-time'>• <span id='reading-time'></span> min read</span>
                          </b:if>
                        </div>
                      </div>
                    </header>

                    <!-- Post Body -->
                    <div class='post-body entry-content' itemprop='articleBody'>
                      <b:if cond='data:view.isSingleItem'>
                        <!-- Table of Contents for Single Posts -->
                        <div class='table-of-contents' id='toc-container' style='display: none;'>
                          <h3>Table of Contents</h3>
                          <ul id='toc-list'></ul>
                        </div>
                      </b:if>

                      <data:post.body/>

                      <!-- Contact Form for Contact Page -->
                      <b:if cond='data:view.isPage and data:post.title contains &quot;Contact&quot;'>
                        <div class='contact-form-container'>
                          <form class='contact-form' action='#' method='post'>
                            <div class='form-group'>
                              <label for='contact-name'>Name *</label>
                              <input type='text' id='contact-name' name='name' required='required'/>
                            </div>
                            <div class='form-group'>
                              <label for='contact-email'>Email *</label>
                              <input type='email' id='contact-email' name='email' required='required'/>
                            </div>
                            <div class='form-group'>
                              <label for='contact-subject'>Subject</label>
                              <input type='text' id='contact-subject' name='subject'/>
                            </div>
                            <div class='form-group'>
                              <label for='contact-message'>Message *</label>
                              <textarea id='contact-message' name='message' rows='5' required='required'></textarea>
                            </div>
                            <button type='submit' class='submit-button'>Send Message</button>
                          </form>
                        </div>
                      </b:if>
                    </div>

                    <!-- Post Footer (only for single posts) -->
                    <b:if cond='data:view.isSingleItem'>
                      <footer class='post-footer'>
                        <!-- Tags -->
                        <b:if cond='data:post.labels'>
                          <div class='post-labels'>
                            <strong>Tags: </strong>
                            <b:loop values='data:post.labels' var='label'>
                              <a expr:href='data:label.url' rel='tag' class='tag-link'><data:label.name/></a>
                            </b:loop>
                          </div>
                        </b:if>

                        <!-- Social Share Buttons -->
                        <div class='social-share'>
                          <h4>Share this article:</h4>
                          <div class='share-buttons'>
                            <a class='share-button facebook' expr:href='&quot;https://www.facebook.com/sharer/sharer.php?u=&quot; + data:post.url.canonical' target='_blank' rel='noopener' aria-label='Share on Facebook'>
                              <span class='icon'>📘</span>
                              <span class='text'>Facebook</span>
                            </a>
                            <a class='share-button twitter' expr:href='&quot;https://twitter.com/intent/tweet?url=&quot; + data:post.url.canonical + &quot;&amp;text=&quot; + data:post.title.escaped' target='_blank' rel='noopener' aria-label='Share on Twitter'>
                              <span class='icon'>🐦</span>
                              <span class='text'>Twitter</span>
                            </a>
                            <a class='share-button linkedin' expr:href='&quot;https://www.linkedin.com/sharing/share-offsite/?url=&quot; + data:post.url.canonical' target='_blank' rel='noopener' aria-label='Share on LinkedIn'>
                              <span class='icon'>💼</span>
                              <span class='text'>LinkedIn</span>
                            </a>
                            <a class='share-button whatsapp' expr:href='&quot;https://wa.me/?text=&quot; + data:post.title.escaped + &quot; &quot; + data:post.url.canonical' target='_blank' rel='noopener' aria-label='Share on WhatsApp'>
                              <span class='icon'>💬</span>
                              <span class='text'>WhatsApp</span>
                            </a>
                            <a class='share-button email' expr:href='&quot;mailto:?subject=&quot; + data:post.title.escaped + &quot;&amp;body=Check out this article: &quot; + data:post.url.canonical' aria-label='Share via Email'>
                              <span class='icon'>📧</span>
                              <span class='text'>Email</span>
                            </a>
                          </div>
                        </div>

                        <!-- Author Bio -->
                        <b:if cond='data:post.author.aboutMe'>
                          <div class='author-bio'>
                            <div class='author-avatar-large'>
                              <b:if cond='data:post.author.authorPhoto.image'>
                                <img expr:src='data:post.author.authorPhoto.image' expr:alt='data:post.author.name' width='80' height='80'/>
                              </b:if>
                            </div>
                            <div class='author-info'>
                              <h4 class='author-name'>About <data:post.author.name/></h4>
                              <p class='author-description'><data:post.author.aboutMe/></p>
                              <b:if cond='data:post.author.profileUrl'>
                                <a expr:href='data:post.author.profileUrl' class='author-link' target='_blank' rel='noopener'>View Profile</a>
                              </b:if>
                            </div>
                          </div>
                        </b:if>

                        <!-- Related Posts Section -->
                        <div class='related-posts'>
                          <h3>You May Also Like</h3>
                          <div class='related-posts-grid' id='related-posts-container'>
                            <!-- Related posts will be populated by JavaScript -->
                          </div>
                        </div>

                        <!-- Post Navigation -->
                        <nav class='post-navigation'>
                          <b:if cond='data:newerPageUrl'>
                            <a expr:href='data:newerPageUrl' class='nav-previous' rel='prev'>
                              <span class='nav-direction'>← Previous</span>
                              <span class='nav-title'>Previous Article</span>
                            </a>
                          </b:if>
                          <b:if cond='data:olderPageUrl'>
                            <a expr:href='data:olderPageUrl' class='nav-next' rel='next'>
                              <span class='nav-direction'>Next →</span>
                              <span class='nav-title'>Next Article</span>
                            </a>
                          </b:if>
                        </nav>
                      </footer>
                    </b:if>
                  </article>
                </b:loop>
              </b:if>
            </b:includable>

            <b:includable id='nextprev'>
              <b:if cond='data:newerPageUrl or data:olderPageUrl'>
                <nav class='blog-pager' aria-label='Pagination'>
                  <b:if cond='data:newerPageUrl'>
                    <a expr:href='data:newerPageUrl' rel='prev'>← Newer Posts</a>
                  </b:if>
                  <b:if cond='data:olderPageUrl'>
                    <a expr:href='data:olderPageUrl' rel='next'>Older Posts →</a>
                  </b:if>
                </nav>
              </b:if>
            </b:includable>
          </b:widget>
        </b:section>
      </section>
    </div>

    <!-- Sidebar -->
    <aside class='sidebar' aria-label='Sidebar'>
      <b:section class='sidebar' id='sidebar' showaddelement='yes'>
        <!-- Search Widget -->
        <b:widget id='HTML1' locked='false' title='Search' type='HTML' version='2'>
          <b:widget-settings>
            <b:widget-setting name='content'>
              <div class='widget'>
                <h3 class='widget-title'>Search</h3>
                <form class='search-form' action='/search' method='get' role='search'>
                  <input class='search-input' name='q' placeholder='Search WebToolsKit...' type='search' aria-label='Search'/>
                  <button class='search-button' type='submit' aria-label='Submit search'>🔍</button>
                </form>
              </div>
            </b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <data:content/>
          </b:includable>
        </b:widget>

        <!-- About Widget -->
        <b:widget id='HTML2' locked='false' title='About WebToolsKit' type='HTML' version='2'>
          <b:widget-settings>
            <b:widget-setting name='content'>
              <div class='widget'>
                <h3 class='widget-title'>About WebToolsKit</h3>
                <p>WebToolsKit provides professional web development tools, SEO utilities, and comprehensive resources for developers and digital marketers.</p>
                <p>Our mission is to empower web professionals with cutting-edge tools and knowledge to build better websites and digital experiences.</p>
                <a href='https://webtoolskit.org/about' class='tool-link'>Learn More</a>
              </div>
            </b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <data:content/>
          </b:includable>
        </b:widget>

        <!-- Popular Posts Widget -->
        <b:widget id='PopularPosts1' locked='false' title='Popular Posts' type='PopularPosts' version='2'>
          <b:widget-settings>
            <b:widget-setting name='numItemsToShow'>5</b:widget-setting>
            <b:widget-setting name='showThumbnails'>true</b:widget-setting>
            <b:widget-setting name='showSnippets'>true</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='widget'>
              <b:include name='title'/>
              <div class='widget-content'>
                <b:loop values='data:posts' var='post'>
                  <div class='popular-post' style='margin-bottom: 1rem; padding-bottom: 1rem; border-bottom: 1px solid var(--border-color);'>
                    <b:if cond='data:post.thumbnail'>
                      <img expr:src='data:post.thumbnail' expr:alt='data:post.title' style='width: 60px; height: 60px; object-fit: cover; border-radius: 4px; float: left; margin-right: 1rem;'/>
                    </b:if>
                    <div>
                      <a expr:href='data:post.href' style='color: var(--text-primary); text-decoration: none; font-weight: 500; font-size: 0.9rem; line-height: 1.4;'><data:post.title/></a>
                      <b:if cond='data:post.snippet'>
                        <p style='color: var(--text-secondary); font-size: 0.8rem; margin: 0.25rem 0 0 0; line-height: 1.3;'><data:post.snippet/></p>
                      </b:if>
                    </div>
                    <div style='clear: both;'></div>
                  </div>
                </b:loop>
              </div>
            </div>
          </b:includable>

          <b:includable id='title'>
            <h3 class='widget-title'><data:title/></h3>
          </b:includable>
        </b:widget>

        <!-- Categories Widget -->
        <b:widget id='Label1' locked='false' title='Categories' type='Label' version='2'>
          <b:widget-settings>
            <b:widget-setting name='sorting'>ALPHA</b:widget-setting>
            <b:widget-setting name='display'>LIST</b:widget-setting>
            <b:widget-setting name='selectedLabelsList'></b:widget-setting>
            <b:widget-setting name='showType'>ALL</b:widget-setting>
            <b:widget-setting name='showFreqNumbers'>true</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='widget'>
              <b:include name='title'/>
              <div class='widget-content'>
                <b:loop values='data:labels' var='label'>
                  <div style='margin-bottom: 0.5rem;'>
                    <a expr:href='data:label.url' style='color: var(--primary-color); text-decoration: none; font-size: 0.9rem;'>
                      <data:label.name/>
                      <span style='color: var(--text-secondary); font-size: 0.8rem;'>(<data:label.count/>)</span>
                    </a>
                  </div>
                </b:loop>
              </div>
            </div>
          </b:includable>

          <b:includable id='title'>
            <h3 class='widget-title'><data:title/></h3>
          </b:includable>
        </b:widget>

        <!-- Quick Links Widget -->
        <b:widget id='LinkList1' locked='false' title='Quick Links' type='LinkList' version='2'>
          <b:widget-settings>
            <b:widget-setting name='sorting'>NONE</b:widget-setting>
            <b:widget-setting name='text-0'>SEO Tools</b:widget-setting>
            <b:widget-setting name='link-0'>https://webtoolskit.org/seo-tools</b:widget-setting>
            <b:widget-setting name='text-1'>Code Formatters</b:widget-setting>
            <b:widget-setting name='link-1'>https://webtoolskit.org/code-formatters</b:widget-setting>
            <b:widget-setting name='text-2'>Image Tools</b:widget-setting>
            <b:widget-setting name='link-2'>https://webtoolskit.org/image-tools</b:widget-setting>
            <b:widget-setting name='text-3'>Security Tools</b:widget-setting>
            <b:widget-setting name='link-3'>https://webtoolskit.org/security-tools</b:widget-setting>
            <b:widget-setting name='text-4'>Contact Us</b:widget-setting>
            <b:widget-setting name='link-4'>https://webtoolskit.org/contact</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <div class='widget'>
              <b:include name='title'/>
              <div class='widget-content'>
                <b:loop values='data:links' var='link'>
                  <div style='margin-bottom: 0.75rem;'>
                    <a expr:href='data:link.target' style='color: var(--primary-color); text-decoration: none; font-size: 0.9rem; display: flex; align-items: center; gap: 0.5rem;'>
                      <span style='color: var(--accent-color);'>→</span>
                      <data:link.name/>
                    </a>
                  </div>
                </b:loop>
              </div>
            </div>
          </b:includable>

          <b:includable id='title'>
            <h3 class='widget-title'><data:title/></h3>
          </b:includable>
        </b:widget>

        <!-- Newsletter Widget -->
        <b:widget id='HTML3' locked='false' title='Newsletter' type='HTML' version='2'>
          <b:widget-settings>
            <b:widget-setting name='content'>
              <div class='widget'>
                <h3 class='widget-title'>Stay Updated</h3>
                <p style='color: var(--text-secondary); font-size: 0.9rem; margin-bottom: 1rem;'>Get the latest web tools and tips delivered to your inbox.</p>
                <form class='search-form' style='flex-direction: column; gap: 0.75rem;'>
                  <input class='search-input' type='email' placeholder='Enter your email' required='required' style='margin: 0;'/>
                  <button class='search-button' type='submit' style='padding: 0.75rem; margin: 0;'>Subscribe</button>
                </form>
              </div>
            </b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <data:content/>
          </b:includable>
        </b:widget>
      </b:section>
    </aside>
  </main>

  <!-- Footer -->
  <footer class='footer-wrapper'>
    <div class='footer-container'>
      <b:section class='footer' id='footer' maxwidgets='3' showaddelement='yes'>
        <b:widget id='HTML4' locked='true' title='Footer' type='HTML' version='2'>
          <b:widget-settings>
            <b:widget-setting name='content'>
              <div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 2rem; text-align: left;'>
                <div>
                  <h4 style='color: white; margin-bottom: 1rem; font-size: 1.1rem;'>WebToolsKit</h4>
                  <p style='color: #d1d5db; line-height: 1.6; margin-bottom: 1rem;'>Professional web development tools, SEO utilities, and comprehensive resources for developers and digital marketers.</p>
                  <div style='display: flex; gap: 1rem;'>
                    <a href='#' style='color: #d1d5db; font-size: 1.5rem; text-decoration: none;' aria-label='Facebook'>📘</a>
                    <a href='#' style='color: #d1d5db; font-size: 1.5rem; text-decoration: none;' aria-label='Twitter'>🐦</a>
                    <a href='#' style='color: #d1d5db; font-size: 1.5rem; text-decoration: none;' aria-label='LinkedIn'>💼</a>
                    <a href='#' style='color: #d1d5db; font-size: 1.5rem; text-decoration: none;' aria-label='GitHub'>🐙</a>
                  </div>
                </div>
                <div>
                  <h4 style='color: white; margin-bottom: 1rem; font-size: 1.1rem;'>Tools</h4>
                  <ul style='list-style: none; padding: 0; margin: 0;'>
                    <li style='margin-bottom: 0.5rem;'><a href='https://webtoolskit.org/seo-analyzer' style='color: #d1d5db; text-decoration: none;'>SEO Analyzer</a></li>
                    <li style='margin-bottom: 0.5rem;'><a href='https://webtoolskit.org/code-formatter' style='color: #d1d5db; text-decoration: none;'>Code Formatter</a></li>
                    <li style='margin-bottom: 0.5rem;'><a href='https://webtoolskit.org/image-optimizer' style='color: #d1d5db; text-decoration: none;'>Image Optimizer</a></li>
                    <li style='margin-bottom: 0.5rem;'><a href='https://webtoolskit.org/password-generator' style='color: #d1d5db; text-decoration: none;'>Password Generator</a></li>
                  </ul>
                </div>
                <div>
                  <h4 style='color: white; margin-bottom: 1rem; font-size: 1.1rem;'>Resources</h4>
                  <ul style='list-style: none; padding: 0; margin: 0;'>
                    <li style='margin-bottom: 0.5rem;'><a href='https://webtoolskit.org/blog' style='color: #d1d5db; text-decoration: none;'>Blog</a></li>
                    <li style='margin-bottom: 0.5rem;'><a href='https://webtoolskit.org/tutorials' style='color: #d1d5db; text-decoration: none;'>Tutorials</a></li>
                    <li style='margin-bottom: 0.5rem;'><a href='https://webtoolskit.org/documentation' style='color: #d1d5db; text-decoration: none;'>Documentation</a></li>
                    <li style='margin-bottom: 0.5rem;'><a href='https://webtoolskit.org/api' style='color: #d1d5db; text-decoration: none;'>API</a></li>
                  </ul>
                </div>
                <div>
                  <h4 style='color: white; margin-bottom: 1rem; font-size: 1.1rem;'>Company</h4>
                  <ul style='list-style: none; padding: 0; margin: 0;'>
                    <li style='margin-bottom: 0.5rem;'><a href='https://webtoolskit.org/about' style='color: #d1d5db; text-decoration: none;'>About Us</a></li>
                    <li style='margin-bottom: 0.5rem;'><a href='https://webtoolskit.org/contact' style='color: #d1d5db; text-decoration: none;'>Contact</a></li>
                    <li style='margin-bottom: 0.5rem;'><a href='https://webtoolskit.org/privacy' style='color: #d1d5db; text-decoration: none;'>Privacy Policy</a></li>
                    <li style='margin-bottom: 0.5rem;'><a href='https://webtoolskit.org/terms' style='color: #d1d5db; text-decoration: none;'>Terms of Service</a></li>
                  </ul>
                </div>
              </div>
              <div style='border-top: 1px solid #4b5563; padding-top: 2rem; text-align: center;'>
                <p style='color: #d1d5db; margin: 0;'>© 2024 WebToolsKit. All rights reserved. | Professional Web Tools and Resources</p>
                <p style='color: #9ca3af; margin: 0.5rem 0 0 0; font-size: 0.9rem;'>Empowering developers and digital marketers with cutting-edge tools.</p>
              </div>
            </b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'>
            <data:content/>
          </b:includable>
        </b:widget>
      </b:section>
    </div>
  </footer>

  <!-- Performance and SEO Scripts -->
  <script type='application/ld+json'>
  {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "WebToolsKit",
    "url": "https://webtoolskit.org",
    "logo": "https://webtoolskit.org/logo.png",
    "description": "Professional web development tools, SEO utilities, and comprehensive resources for developers and digital marketers.",
    "sameAs": [
      "https://twitter.com/webtoolskit",
      "https://facebook.com/webtoolskit",
      "https://linkedin.com/company/webtoolskit"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "url": "https://webtoolskit.org/contact"
    }
  }
  </script>

  <!-- Enhanced Functionality Script -->
  <script>
  //<![CDATA[
  // Lazy loading for images
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    });

    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img);
    });
  }

  // Smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Search form enhancement
  const searchForm = document.querySelector('.search-form');
  if (searchForm) {
    searchForm.addEventListener('submit', function(e) {
      const searchInput = this.querySelector('.search-input');
      if (!searchInput.value.trim()) {
        e.preventDefault();
        searchInput.focus();
      }
    });
  }

  // Mobile menu toggle (if needed)
  const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
  const mainNav = document.querySelector('.main-nav');
  if (mobileMenuToggle && mainNav) {
    mobileMenuToggle.addEventListener('click', function() {
      mainNav.classList.toggle('active');
      this.setAttribute('aria-expanded', mainNav.classList.contains('active'));
    });
  }

  // Performance monitoring
  if ('PerformanceObserver' in window) {
    // Monitor Core Web Vitals
    function sendToAnalytics(metric) {
      // Send to your analytics service
      console.log('Core Web Vital:', metric);
    }

    // LCP
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        sendToAnalytics({
          name: 'LCP',
          value: entry.startTime,
          id: 'lcp'
        });
      }
    }).observe({entryTypes: ['largest-contentful-paint']});

    // FID (replaced by INP)
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        sendToAnalytics({
          name: 'FID',
          value: entry.processingStart - entry.startTime,
          id: 'fid'
        });
      }
    }).observe({entryTypes: ['first-input']});

    // CLS
    let clsValue = 0;
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          sendToAnalytics({
            name: 'CLS',
            value: clsValue,
            id: 'cls'
          });
        }
      }
    }).observe({entryTypes: ['layout-shift']});
  }

  // Service Worker registration for PWA capabilities
  if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
      navigator.serviceWorker.register('/sw.js').then(function(registration) {
        console.log('SW registered: ', registration);
      }).catch(function(registrationError) {
        console.log('SW registration failed: ', registrationError);
      });
    });
  }
  //]]>
  </script>

  <!-- Google Analytics 4 (Replace with your tracking ID) -->
  <script async='async' src='https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID'></script>
  <script>
  //<![CDATA[
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
  //]]>
  </script>
</body>
</html>
